from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from django.urls import re_path
from django.http import HttpResponse, Http404, FileResponse
from rest_framework.authtoken.models import Token
import os
import mimetypes

def secure_media_serve(request, path):
    """
    Secure media file serving with authentication
    """
    print(f"DEBUG: ===== SECURE MEDIA SERVE CALLED =====")
    print(f"DEBUG: Path: {path}")
    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: Request URL: {request.build_absolute_uri()}")
    print(f"DEBUG: Request headers: {dict(request.META)}")

    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        print("DEBUG: Handling CORS preflight request")
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, HEAD, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'
        response['Access-Control-Max-Age'] = '86400'
        return response

    # Check for token authentication
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    print(f"DEBUG: Auth header: {auth_header}")

    if auth_header.startswith('Token '):
        token_key = auth_header.split(' ')[1]
        try:
            token = Token.objects.get(key=token_key)
            user = token.user
            print(f"DEBUG: Authenticated user via token: {user.username}")
        except Token.DoesNotExist:
            print("DEBUG: Token not found")
            return HttpResponse('Unauthorized - Invalid token', status=401)
    elif request.user.is_authenticated:
        user = request.user
        print(f"DEBUG: Authenticated user via session: {user.username}")
    else:
        print("DEBUG: No authentication found")
        return HttpResponse('Unauthorized - No authentication', status=401)

    # Construct the full file path
    full_path = os.path.join(settings.MEDIA_ROOT, path)
    print(f"DEBUG: Full file path: {full_path}")
    print(f"DEBUG: MEDIA_ROOT: {settings.MEDIA_ROOT}")

    # Check if file exists
    if not os.path.exists(full_path):
        print(f"DEBUG: File does not exist: {full_path}")
        raise Http404("File not found")

    print(f"DEBUG: File exists, size: {os.path.getsize(full_path)} bytes")

    # For user_files, check if the user owns the file
    if path.startswith('user_files/'):
        from inspection.models import UserFile
        try:
            print(f"DEBUG: Checking user file ownership for path: {path}")
            # Check if user has access to this file by matching the file path
            user_file = UserFile.objects.filter(user=user, file__icontains=path).first()
            if not user_file:
                # Try with just the filename
                filename = os.path.basename(path)
                print(f"DEBUG: Trying with filename: {filename}")
                user_file = UserFile.objects.filter(user=user, file__icontains=filename).first()
            if not user_file:
                print(f"DEBUG: No matching user file found for user {user.username}")
                return HttpResponse('Forbidden - File not owned by user', status=403)
            print(f"DEBUG: User file found: {user_file.name}")
        except Exception as e:
            print(f"DEBUG: Exception checking file ownership: {str(e)}")
            return HttpResponse(f'Error checking file ownership: {str(e)}', status=500)

    # For inspection photos (including anomaly photos), check if the user owns the inspection
    elif path.startswith('inspection_photos/') or path.startswith('anomaly_photos/'):
        from inspection.models import Inspection
        from django.db import models
        try:
            print(f"DEBUG: Checking inspection photo ownership for path: {path}")
            filename = os.path.basename(path)

            if path.startswith('inspection_photos/'):
                # Check if user has an inspection with this photo
                inspection = Inspection.objects.filter(
                    created_by=user
                ).filter(
                    models.Q(photo_marquage__icontains=filename) |
                    models.Q(photo_equipement__icontains=filename)
                ).first()
            else:  # anomaly_photos/
                # Check if user has an inspection with this anomaly photo
                inspection = Inspection.objects.filter(
                    created_by=user,
                    photos_anomalies__icontains=filename
                ).first()

            if not inspection:
                print(f"DEBUG: No matching inspection found for user {user.username} and photo {filename}")
                return HttpResponse('Forbidden - Photo not owned by user', status=403)
            print(f"DEBUG: Inspection found: {inspection.fiche_num}")
        except Exception as e:
            print(f"DEBUG: Exception checking inspection photo ownership: {str(e)}")
            return HttpResponse(f'Error checking photo ownership: {str(e)}', status=500)

    # Serve the file
    try:
        content_type = mimetypes.guess_type(full_path)[0] or 'application/octet-stream'
        print(f"DEBUG: Serving file with content type: {content_type}")

        # Open file and create response
        file_handle = open(full_path, 'rb')
        response = FileResponse(
            file_handle,
            content_type=content_type,
            as_attachment=False  # Set to False for inline viewing
        )

        # Add proper headers for file serving
        response['Content-Length'] = os.path.getsize(full_path)
        response['Accept-Ranges'] = 'bytes'

        # Add CORS headers for cross-origin requests
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, HEAD, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'

        print(f"DEBUG: File response created successfully, size: {os.path.getsize(full_path)} bytes")
        return response

    except IOError as e:
        print(f"DEBUG: IOError serving file: {str(e)}")
        raise Http404("File not found")

def test_media_view(request):
    """Simple test view to verify URL routing"""
    return HttpResponse("Media URL routing is working!")

def config_health_check(request):
    """Health check for config project"""
    from django.http import JsonResponse
    return JsonResponse({
        "status": "healthy",
        "message": "Config project is running",
        "settings_module": "config.settings",
        "project": "config"
    })

urlpatterns = [
    path('', RedirectView.as_view(url='/api/inspections/')),
    path('health/', config_health_check, name='config_health'),
    path('admin/', admin.site.urls),
    path('api/auth/', include('authentication.urls')),
    path('api/inspections/', include('inspection.urls')),
    # Test media routing
    path('media/test/', test_media_view, name='media_test'),
    # Secure media serving
    re_path(r'^media/(?P<path>.*)$', secure_media_serve, name='secure_media'),
]

# Serve static files
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
